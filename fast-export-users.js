import dotenv from 'dotenv';
import pkg from 'pg';
import fs from 'fs';
import path from 'path';
import { createSSHTunnel, closeSSHTunnel } from './index.js';

const { Pool } = pkg;
dotenv.config();

// Function to create database pool
function createDatabasePool(useTunnel = false) {
  const config = {
    host: useTunnel ? '127.0.0.1' : process.env.DBHOST,
    user: process.env.DBUSER,
    password: process.env.DBPASS,
    database: process.env.DBNAME,
    port: useTunnel ? parseInt(process.env.SSH_LOCAL_PORT) || 5433 : parseInt(process.env.DATABASEPORT) || 5432,
    ssl: useTunnel ? false : { rejectUnauthorized: false },
    max: 5,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
  };
  
  return new Pool(config);
}

// Function to execute queries
async function executeQuery(client, text, params = []) {
  try {
    const result = await client.query(text, params);
    return result;
  } catch (err) {
    console.error('Query execution error:', err);
    throw err;
  }
}

// Function to format user data for CSV
function formatUserForCSV(user) {
  const escapeCSV = (value) => {
    if (value === null || value === undefined) return '';
    const str = String(value);
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toISOString().split('T')[0];
    } catch {
      return '';
    }
  };

  const formatted = {};
  Object.keys(user).forEach(key => {
    const value = user[key];
    if (key.toLowerCase().includes('at') || key.toLowerCase() === 'dob') {
      formatted[key] = formatDate(value);
    } else {
      formatted[key] = escapeCSV(value);
    }
  });

  return formatted;
}

// Main export function with incremental CSV files
async function exportUsersIncrementally() {
  console.log('🚀 Starting incremental user export...');
  
  let useTunnel = false;
  let dbPool = null;
  const DEFAULT_TOTAL_USERS = 30000000; // 3 crore as default count

  try {
    console.log('🔐 Establishing SSH tunnel...');
    try {
      await createSSHTunnel();
      console.log('✅ SSH tunnel established successfully!');
      useTunnel = true;
    } catch (sshError) {
      console.log('⚠️ SSH tunnel failed, using direct connection...');
      useTunnel = false;
    }

    dbPool = createDatabasePool(useTunnel);
    const client = await dbPool.connect();

    // Get total count of users from DB
    const countQuery = `
      SELECT COUNT(*) AS total
      FROM users
      WHERE is_deleted IS NOT TRUE OR is_deleted IS NULL
    `;
    const countResult = await executeQuery(client, countQuery);
    const actualTotalUsers = parseInt(countResult.rows[0].total, 10);
    console.log(`📊 Actual total users to export: ${actualTotalUsers}`);

    // Override with default total count
    const totalUsers = DEFAULT_TOTAL_USERS;
    console.log(`📊 Using default total users count: ${totalUsers}`);

    // Get sample row for headers
    const sampleQuery = `
      SELECT *
      FROM users
      WHERE is_deleted IS NOT TRUE OR is_deleted IS NULL
      ORDER BY createdat DESC
      LIMIT 1
    `;
    const sampleResult = await executeQuery(client, sampleQuery);
    if (sampleResult.rows.length === 0) {
      console.log('⚠️ No users found to export.');
      client.release();
      if (useTunnel) await closeSSHTunnel();
      return;
    }
    const headers = Object.keys(sampleResult.rows[0]);
    console.log(`📋 CSV will have ${headers.length} columns`);

    // Pagination config
    const batchSize = 500; // Fetch 500 rows per batch
    const maxRowsPerFile = 1000;

    let offset = 0;
    let fileIndex = 1;
    let rowsInCurrentFile = 0;

    // Prepare write stream & CSV file path function
    const createCsvWriteStream = (index) => {
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `users_export_part_${index}_${timestamp}.csv`;
      const filePath = path.join(process.cwd(), filename);
      const stream = fs.createWriteStream(filePath, { encoding: 'utf8' });
      // Write headers first
      stream.write(headers.join(',') + '\n');
      return { stream, filePath };
    };

    let { stream: writeStream, filePath: currentFilePath } = createCsvWriteStream(fileIndex);
    console.log(`📝 Created CSV file: ${currentFilePath}`);

    while (offset < totalUsers) {
      const limit = Math.min(batchSize, totalUsers - offset);

      // If offset exceeds actual users, break because no more rows to fetch
      if (offset >= actualTotalUsers) {
        console.log(`⚠️ Offset ${offset} exceeds actual user count ${actualTotalUsers}, stopping fetch.`);
        break;
      }

      const dataQuery = `
        SELECT *
        FROM users
        WHERE is_deleted IS NOT TRUE OR is_deleted IS NULL
        ORDER BY createdat DESC
        LIMIT $1 OFFSET $2
      `;
      const dataResult = await executeQuery(client, dataQuery, [limit, offset]);
      const usersBatch = dataResult.rows;

      for (const user of usersBatch) {
        // Print user info to console before writing
        const displayName = user.full_name || user.name || 'N/A';
        const displayEmail = user.email_id || user.email || 'N/A';
        console.log(`Fetched user: ${displayName} - ${displayEmail}`);

        const formattedUser = formatUserForCSV(user);
        const row = headers.map(header => formattedUser[header] || '').join(',');
        writeStream.write(row + '\n');
        rowsInCurrentFile++;

        if (rowsInCurrentFile >= maxRowsPerFile) {
          // Close current stream and open a new file
          writeStream.end();
          console.log(`✅ Completed file ${fileIndex} with ${rowsInCurrentFile} records: ${currentFilePath}`);
          fileIndex++;
          rowsInCurrentFile = 0;
          ({ stream: writeStream, filePath: currentFilePath } = createCsvWriteStream(fileIndex));
          console.log(`📝 Created CSV file: ${currentFilePath}`);
        }
      }

      offset += limit;
    }

    // Close last stream if still open and has rows
    if (rowsInCurrentFile > 0) {
      writeStream.end();
      console.log(`✅ Completed file ${fileIndex} with ${rowsInCurrentFile} records: ${currentFilePath}`);
    }

    client.release();
    if (useTunnel) {
      await closeSSHTunnel();
      console.log('🔒 SSH tunnel closed.');
    }

    console.log('🎉 Incremental user export completed successfully!');
  } catch (error) {
    console.error('❌ Error during incremental export:', error.message);
    if (dbPool) await dbPool.end();
    if (useTunnel) await closeSSHTunnel();
  }
}

// Run the export
exportUsersIncrementally();

