const fs = require('fs');
const csv = require('csv-parser');

const inputFilePath = 'path/to/your/largefile.csv'; // Change this to your CSV file path
const outputFilePath = 'path/to/output/directory/'; // Change this to your output directory
const totalRows = 69919815; // Set the total number of rows in the CSV file
const maxRowsPerFile = 100000; // Set the number of rows per output file

let currentFileIndex = 0;
let currentRowCount = 0;
let currentOutputStream = null;

function createNewOutputStream() {
    if (currentOutputStream) {
        currentOutputStream.end(); // Close the previous stream
    }
    const outputFileName = `${outputFilePath}output_${currentFileIndex}.csv`;
    currentOutputStream = fs.createWriteStream(outputFileName);
    currentOutputStream.write('Column1,Column2,Column3\n'); // Write header (adjust as necessary)
    currentRowCount = 0;
    currentFileIndex++;
}

async function splitCsv() {
    createNewOutputStream(); // Create the first output stream

    const readStream = fs.createReadStream(inputFilePath)
        .pipe(csv());

    for await (const row of readStream) {
        currentOutputStream.write(`${row.Column1},${row.Column2},${row.Column3}\n`); // Adjust column names
        currentRowCount++;

        if (currentRowCount >= maxRowsPerFile) {
            createNewOutputStream(); // Create a new output stream after reaching the limit
        }
    }

    if (currentOutputStream) {
        currentOutputStream.end(); // Close the last stream
    }
    console.log('CSV file has been processed and split into smaller files.');
}

splitCsv().catch(error => {
    console.error('Error processing the CSV file:', error);
});
